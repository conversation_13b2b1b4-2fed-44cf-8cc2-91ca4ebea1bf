<template>
  <div class="new-clue-container">
    <!-- 左侧表单区域 -->
    <div class="form-section">
      <div class="form-header">
        <h2>新增线索</h2>
        <p>从邮件 "{{ email.subject }}" 创建线索</p>
      </div>

      <div class="form-body">
        <!-- 基本信息 -->
        <div class="form-section-title">基本信息</div>

        <div class="form-row">
          <div class="form-group half-width">
            <label><span class="required">*</span>线索主题</label>
            <div class="form-control">
              <input type="text" v-model="clueData.subject" placeholder="" />
            </div>
          </div>
          <div class="form-group half-width">
            <label><span class="required">*</span>小线索主题</label>
            <div class="form-control">
              <input type="text" v-model="clueData.subSubject" placeholder="" />
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group quarter-width">
            <label>线索评分</label>
            <div class="form-control">
              <select v-model="clueData.score">
                <option value="">请选择</option>
                <option value="1">1分</option>
                <option value="2">2分</option>
                <option value="3">3分</option>
                <option value="4">4分</option>
                <option value="5">5分</option>
              </select>
            </div>
          </div>
          <div class="form-group quarter-width">
            <label>问题时间</label>
            <div class="form-control">
              <input type="datetime-local" v-model="clueData.questionTime" />
            </div>
          </div>
          <div class="form-group quarter-width">
            <label>需求数量</label>
            <div class="form-control">
              <input type="number" v-model="clueData.demandQuantity" placeholder="0.00" />
            </div>
          </div>
          <div class="form-group quarter-width">
            <label>附件</label>
            <div class="form-control">
              <div class="attachment-upload">
                <upload-icon class="upload-icon" />
                <span>上传附件</span>
              </div>
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group half-width vertical">
            <label>营销/问题描述</label>
            <div class="form-control">
              <textarea v-model="clueData.description" placeholder="" rows="3"></textarea>
            </div>
          </div>
          <div class="form-group quarter-width">
            <label><span class="required">*</span>线索收件人</label>
            <div class="form-control">
              <select v-model="clueData.recipient">
                <option value="">请选择</option>
                <option value="sales">销售部</option>
                <option value="marketing">市场部</option>
              </select>
            </div>
          </div>
          <div class="form-group quarter-width">
            <label><span class="required">*</span>线索收件部门</label>
            <div class="form-control">
              <select v-model="clueData.department">
                <option value="">请选择</option>
                <option value="sales">销售部</option>
                <option value="marketing">市场部</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 客户信息 -->
        <div class="form-section-title">客户信息</div>

        <div class="form-row">
          <div class="form-group quarter-width">
            <label><span class="required">*</span>客户名称</label>
            <div class="form-control">
              <select v-model="clueData.customerName">
                <option value="">请选择</option>
                <option value="customer1">客户1</option>
                <option value="customer2">客户2</option>
              </select>
            </div>
          </div>
          <div class="form-group quarter-width">
            <label>区域</label>
            <div class="form-control">
              <input type="text" v-model="clueData.region" placeholder="" />
            </div>
          </div>
          <div class="form-group quarter-width">
            <label>包装</label>
            <div class="form-control">
              <input type="text" v-model="clueData.packaging" placeholder="" />
            </div>
          </div>
          <div class="form-group quarter-width">
            <label>时间</label>
            <div class="form-control">
              <input type="text" v-model="clueData.time" placeholder="" />
            </div>
          </div>
        </div>

        <div class="form-row">
          <div class="form-group quarter-width">
            <label>媒体</label>
            <div class="form-control">
              <input type="text" v-model="clueData.media" placeholder="" />
            </div>
          </div>
          <div class="form-group quarter-width">
            <label>总址</label>
            <div class="form-control">
              <input type="text" v-model="clueData.address" placeholder="" />
            </div>
          </div>
          <div class="form-group quarter-width">
            <label>外贸行业</label>
            <div class="form-control">
              <select v-model="clueData.industry">
                <option value="">请选择</option>
                <option value="electronics">电子产品</option>
                <option value="textiles">纺织品</option>
                <option value="machinery">机械设备</option>
              </select>
            </div>
          </div>
          <div class="form-group quarter-width">
            <label>主营产品</label>
            <div class="form-control">
              <select v-model="clueData.mainProduct">
                <option value="">请选择</option>
                <option value="product1">产品1</option>
                <option value="product2">产品2</option>
              </select>
            </div>
          </div>
        </div>

        <div class="form-group vertical">
          <label>客户读</label>
          <div class="form-control">
            <textarea v-model="clueData.customerNotes" placeholder="" rows="2"></textarea>
          </div>
        </div>

        <!-- 邮件原文 -->
        <div class="form-section-title">邮件原文</div>
        <div class="email-original-content">
          <div class="email-preview">
            <div class="email-subject">{{ email.subject }}</div>
            <div class="email-meta-line">
              <span>发件人: {{ email.sender }}</span>
              <span>时间: {{ email.fullDate || email.time }}</span>
            </div>
            <div class="email-body-preview" v-html="email.body"></div>
          </div>
        </div>

        <!-- 线索负责人 -->
        <div class="form-section-title">线索负责人</div>
        <div class="responsibility-tabs">
          <div
            class="tab-item"
            :class="{ active: activeResponsibilityTab === 'online' }"
            @click="activeResponsibilityTab = 'online'"
          >
            线索负责人
          </div>
          <div
            class="tab-item"
            :class="{ active: activeResponsibilityTab === 'products' }"
            @click="activeResponsibilityTab = 'products'"
          >
            在售商品
          </div>
          <div
            class="tab-item"
            :class="{ active: activeResponsibilityTab === 'non-products' }"
            @click="activeResponsibilityTab = 'non-products'"
          >
            非在售商品
          </div>
        </div>

        <div class="tab-content-area">
          <div v-if="activeResponsibilityTab === 'online'" class="responsibility-content">
            <button class="add-responsibility-btn" @click="addResponsibility">
              <plus-icon class="icon-small" />
              添加负责人
            </button>
            <div v-for="(person, index) in clueData.responsiblePersons" :key="index" class="responsibility-item">
              <div class="responsibility-input">
                <input type="text" v-model="person.name" placeholder="负责人姓名" />
              </div>
              <button class="remove-btn" @click="removeResponsibility(index)">
                <x-icon class="icon-small" />
              </button>
            </div>
          </div>
          <div v-else-if="activeResponsibilityTab === 'products'" class="products-content">
            <p class="empty-state">暂无在售商品</p>
          </div>
          <div v-else-if="activeResponsibilityTab === 'non-products'" class="non-products-content">
            <p class="empty-state">暂无非在售商品</p>
          </div>
        </div>

        <!-- 联系人 -->
        <div class="form-section-title">
          联系人
          <button class="add-contact-btn" @click="addContact">
            <plus-icon class="icon-small" />
          </button>
        </div>

        <div v-for="(contact, index) in clueData.contacts" :key="index" class="contact-item">
          <div class="contact-header">
            <span class="contact-number">{{ index + 1 }}</span>
            <button v-if="index > 0" class="remove-contact-btn" @click="removeContact(index)">
              删除
            </button>
          </div>

          <div class="form-row">
            <div class="form-group quarter-width">
              <label>联系人姓名</label>
              <div class="form-control">
                <input type="text" v-model="contact.name" placeholder="jacky.lin@jmsgroup.c" />
              </div>
            </div>
            <div class="form-group quarter-width">
              <label>whatsapp</label>
              <div class="form-control">
                <div class="input-with-action">
                  <input type="text" v-model="contact.whatsapp" placeholder="" />
                  <button class="action-btn">新增</button>
                </div>
              </div>
            </div>
            <div class="form-group quarter-width">
              <label>邮箱号码</label>
              <div class="form-control">
                <div class="input-with-action">
                  <input type="email" v-model="contact.email" placeholder="jacky.lin@jmsgroup" />
                  <button class="action-btn">新增</button>
                </div>
              </div>
            </div>
            <div class="form-group quarter-width">
              <label>电话</label>
              <div class="form-control">
                <div class="input-with-action">
                  <input type="text" v-model="contact.phone" placeholder="" />
                  <button class="action-btn">新增</button>
                </div>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group quarter-width">
              <label>手机</label>
              <div class="form-control">
                <div class="input-with-action">
                  <input type="text" v-model="contact.mobile" placeholder="" />
                  <button class="action-btn">新增</button>
                </div>
              </div>
            </div>
            <div class="form-group quarter-width vertical">
              <label>备注</label>
              <div class="form-control">
                <textarea v-model="contact.notes" placeholder="" rows="1"></textarea>
              </div>
            </div>
            <div class="form-group quarter-width">
              <label>社交</label>
              <div class="form-control">
                <select v-model="contact.social">
                  <option value="">请选择</option>
                  <option value="wechat">微信</option>
                  <option value="qq">QQ</option>
                  <option value="linkedin">LinkedIn</option>
                </select>
              </div>
            </div>
            <div class="form-group quarter-width">
              <label>&nbsp;</label>
              <div class="form-control">
                <div class="action-buttons">
                  <button class="action-btn">新增</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="form-footer">
        <button class="draft-btn" @click="saveDraft">保存草稿</button>
        <button class="submit-btn" @click="saveClue">保存并提交</button>
        <button class="cancel-btn" @click="cancel">取消</button>
      </div>
    </div>

    <!-- 右侧邮件内容区域 -->
    <div class="email-section">
      <div class="email-header">
        <h3>原邮件内容</h3>
      </div>

      <div class="email-content-container">
        <!-- 邮件基本信息 -->
        <div class="email-meta-info">
          <div class="email-title">{{ email.subject }}</div>

          <div class="email-meta">
            <div class="sender-info">
              <div class="detail-label">发件人：</div>
              <div class="detail-value">
                <span class="sender-name">{{ email.sender }}</span>
                <span v-if="email.tag" class="sender-tag">@{{ email.tag }}</span>
              </div>
            </div>
            <div class="email-date">{{ email.fullDate || email.time }}</div>
          </div>

          <div class="email-meta" v-if="email.recipients && email.recipients.length">
            <div class="sender-info">
              <div class="detail-label">收件人：</div>
              <div class="detail-value">
                <span v-for="(recipient, idx) in email.recipients" :key="idx" class="recipient">
                  {{ recipient }}
                </span>
              </div>
            </div>
          </div>

          <!-- 邮件标签 -->
          <div class="email-tags" v-if="email.tags && email.tags.length > 0">
            <span
              v-for="(tagId, tagIndex) in email.tags"
              :key="tagIndex"
              class="email-tag"
              :style="{ backgroundColor: getTagColor(tagId) }"
            >
              {{ getTagName(tagId) }}
            </span>
          </div>
        </div>

        <!-- 附件信息 -->
        <div class="attachments-section" v-if="email.attachments && email.attachments.length > 0">
          <div class="attachments-header">
            <paperclip-icon class="icon-small" />
            附件 ({{ email.attachments.length }})
          </div>
          <div class="attachments-list">
            <div
              v-for="(attachment, index) in email.attachments"
              :key="index"
              class="attachment-item"
            >
              <div class="attachment-info">
                <file-text-icon v-if="isTextFile(attachment.name)" class="file-icon" />
                <image-icon v-else-if="isImageFile(attachment.name)" class="file-icon" />
                <file-icon v-else class="file-icon" />
                <div class="attachment-details">
                  <div class="attachment-name">{{ attachment.name }}</div>
                  <div class="attachment-size">{{ formatFileSize(attachment.size) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 邮件正文内容 -->
        <div class="email-body-section">
          <div class="email-body" v-html="email.body"></div>

          <!-- 邮件签名 -->
          <div class="email-signature" v-if="email.signature">
            <div class="signature-header">签名</div>
            <div class="signature-content" v-html="email.signature"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  PaperclipIcon,
  FileTextIcon,
  ImageIcon,
  FileIcon,
  UploadIcon,
  PlusIcon,
  XIcon
} from 'lucide-vue'

export default {
  name: 'NewClueForm',
  components: {
    PaperclipIcon,
    FileTextIcon,
    ImageIcon,
    FileIcon,
    UploadIcon,
    PlusIcon,
    XIcon
  },
  props: {
    email: {
      type: Object,
      required: true
    },
    tags: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      activeResponsibilityTab: 'online',
      clueData: {
        // 基本信息
        subject: '',
        subSubject: '',
        score: '',
        questionTime: '',
        demandQuantity: '',
        description: '',
        recipient: '',
        department: '',

        // 客户信息
        customerName: '',
        region: '',
        packaging: '',
        time: '',
        media: '',
        address: '',
        industry: '',
        mainProduct: '',
        customerNotes: '',

        // 线索负责人
        responsiblePersons: [],

        // 联系人
        contacts: [
          {
            name: '',
            whatsapp: '',
            email: '',
            phone: '',
            mobile: '',
            notes: '',
            social: ''
          }
        ],

        // 原有字段
        relatedEmailId: null
      }
    }
  },
  created() {
    console.log('🔥 NewClueForm created', {
      email: this.email,
      tags: this.tags
    });
    // 预填充表单数据
    this.prefillFormData();
  },
  methods: {
    prefillFormData() {
      if (this.email) {
        // 从邮件中提取信息
        this.clueData.subject = this.email.subject || '';
        this.clueData.subSubject = this.email.subject || '';
        this.clueData.relatedEmailId = this.email.id;

        // 设置默认问题时间为当前时间
        const now = new Date();
        this.clueData.questionTime = now.toISOString().slice(0, 16);

        // 预填充联系人信息
        if (this.clueData.contacts.length > 0) {
          this.clueData.contacts[0].name = this.email.sender || '';
          this.clueData.contacts[0].email = this.email.sender ? `${this.email.sender.toLowerCase().replace(/\s+/g, '.')}@example.com` : '';
        }

        // 提取邮件内容作为描述
        const emailContent = this.stripHtml(this.email.body || '');
        this.clueData.description = emailContent.substring(0, 200) + (emailContent.length > 200 ? '...' : '');
      }
    },

    stripHtml(html) {
      const tmp = document.createElement('div');
      tmp.innerHTML = html;
      return tmp.textContent || tmp.innerText || '';
    },

    saveClue() {
      // 模拟保存线索
      console.log('保存线索:', this.clueData);

      // 显示成功提示
      this.$emit('save-success', {
        ...this.clueData,
        id: Date.now()
      });
    },

    saveDraft() {
      // 保存草稿
      console.log('保存草稿:', this.clueData);
      this.$emit('save-success', {
        ...this.clueData,
        id: Date.now(),
        status: 'draft'
      });
    },

    cancel() {
      this.$emit('cancel');
    },

    // 负责人管理
    addResponsibility() {
      this.clueData.responsiblePersons.push({ name: '' });
    },

    removeResponsibility(index) {
      this.clueData.responsiblePersons.splice(index, 1);
    },

    // 联系人管理
    addContact() {
      this.clueData.contacts.push({
        name: '',
        whatsapp: '',
        email: '',
        phone: '',
        mobile: '',
        notes: '',
        social: ''
      });
    },

    removeContact(index) {
      if (this.clueData.contacts.length > 1) {
        this.clueData.contacts.splice(index, 1);
      }
    },

    // 邮件内容显示相关方法
    getTagColor(tagId) {
      const tag = this.tags.find(t => t.id === tagId);
      return tag ? tag.color : '#f0f0f0';
    },

    getTagName(tagId) {
      const tag = this.tags.find(t => t.id === tagId);
      return tag ? tag.name : '未知标签';
    },

    isTextFile(filename) {
      const textExtensions = ['.txt', '.doc', '.docx', '.pdf', '.rtf'];
      return textExtensions.some(ext => filename.toLowerCase().endsWith(ext));
    },

    isImageFile(filename) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg'];
      return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext));
    },

    formatFileSize(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
  }
}
</script>

<style scoped>
.new-clue-container {
  display: flex;
  height: 100%;
  background-color: #fff;
  gap: 1px;
}

.form-section {
  flex: 0 0 45%;
  padding: 20px;
  background-color: #fff;
  overflow-y: auto;
  border-right: 1px solid #e6e9ed;
}

.email-section {
  flex: 1;
  padding: 20px;
  background-color: #fff;
  overflow-y: auto;
}

.form-header {
  margin-bottom: 24px;
  border-bottom: 1px solid #e6e9ed;
  padding-bottom: 16px;
}

.form-header h2 {
  font-size: 20px;
  margin-bottom: 8px;
  color: #333;
}

.form-header p {
  color: #666;
  font-size: 14px;
}

.form-body {
  margin-bottom: 24px;
}

/* 表单分组样式 */
.form-section-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 24px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 2px solid #409EFF;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  gap: 12px;
}

.form-group.half-width {
  flex: 1;
}

.form-group.quarter-width {
  flex: 0 0 calc(25% - 12px);
}

.form-group label {
  flex: 0 0 auto;
  min-width: 80px;
  max-width: 120px;
  padding-top: 8px;
  font-weight: 500;
  color: #333;
  font-size: 14px;
  line-height: 1.4;
  word-wrap: break-word;
}

.form-group .form-control {
  flex: 1;
  min-width: 0;
}

.required {
  color: #f56c6c;
  margin-right: 4px;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
  box-sizing: border-box;
}

.form-group textarea {
  min-height: 60px;
  resize: vertical;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #409EFF;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

/* 特殊情况：textarea和某些复杂控件需要垂直布局 */
.form-group.vertical {
  flex-direction: column;
  align-items: stretch;
}

.form-group.vertical label {
  margin-bottom: 8px;
  padding-top: 0;
}

.related-email {
  padding: 12px;
  background-color: #f5f7fa;
  border: 1px solid #e6e9ed;
  border-radius: 4px;
}

.email-subject {
  font-weight: 500;
  margin-bottom: 8px;
}

.email-sender,
.email-time {
  font-size: 13px;
  color: #666;
  margin-bottom: 4px;
}

/* 附件上传样式 */
.attachment-upload {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  cursor: pointer;
  color: #666;
  font-size: 14px;
  transition: all 0.2s;
}

.attachment-upload:hover {
  border-color: #409EFF;
  color: #409EFF;
}

.upload-icon {
  width: 16px;
  height: 16px;
  margin-right: 6px;
}

/* 邮件原文预览样式 */
.email-original-content {
  margin-bottom: 20px;
}

.email-preview {
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e6e9ed;
}

.email-subject {
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.email-meta-line {
  font-size: 13px;
  color: #666;
  margin-bottom: 12px;
}

.email-meta-line span {
  margin-right: 16px;
}

.email-body-preview {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  max-height: 150px;
  overflow-y: auto;
}

/* 线索负责人样式 */
.responsibility-tabs {
  display: flex;
  border-bottom: 1px solid #e6e9ed;
  margin-bottom: 16px;
}

.tab-item {
  padding: 8px 16px;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  color: #666;
  font-size: 14px;
  transition: all 0.2s;
}

.tab-item.active {
  color: #409EFF;
  border-bottom-color: #409EFF;
}

.tab-item:hover {
  color: #409EFF;
}

.tab-content-area {
  min-height: 100px;
  margin-bottom: 20px;
}

.add-responsibility-btn {
  display: flex;
  align-items: center;
  padding: 8px 16px;
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 12px;
}

.add-responsibility-btn:hover {
  background-color: #66b1ff;
}

.responsibility-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.responsibility-input {
  flex: 1;
}

.responsibility-item input {
  width: 100%;
}

.remove-btn {
  padding: 4px;
  background-color: #f56c6c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.empty-state {
  text-align: center;
  color: #999;
  padding: 40px 0;
}

/* 联系人样式 */
.form-section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.add-contact-btn {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.contact-item {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e6e9ed;
  border-radius: 6px;
  background-color: #fafafa;
}

.contact-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.contact-number {
  font-weight: 600;
  color: #333;
}

.remove-contact-btn {
  padding: 4px 8px;
  background-color: #f56c6c;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
}

.input-with-action {
  display: flex;
  gap: 8px;
}

.input-with-action input {
  flex: 1;
}

.action-btn {
  padding: 6px;
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
}

.action-btn:hover {
  background-color: #66b1ff;
}

.action-buttons {
  display: flex;
  align-items: flex-end;
  height: 100%;
  padding-top: 24px;
}

.form-footer {
  display: flex;
  justify-content: flex-start;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #e6e9ed;
}

.draft-btn,
.submit-btn,
.cancel-btn {
  padding: 10px 20px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  border: 1px solid;
}

.draft-btn {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #606266;
}

.submit-btn {
  background-color: #67c23a;
  border-color: #67c23a;
  color: white;
}

.cancel-btn {
  background-color: #f5f7fa;
  border-color: #dcdfe6;
  color: #606266;
}

.draft-btn:hover {
  background-color: #e6e9ed;
}

.submit-btn:hover {
  background-color: #85ce61;
}

.cancel-btn:hover {
  background-color: #e6e9ed;
}

/* 邮件内容区域样式 */
.email-header {
  margin-bottom: 20px;
  border-bottom: 1px solid #e6e9ed;
  padding-bottom: 16px;
}

.email-header h3 {
  font-size: 18px;
  color: #333;
  margin: 0;
}

.email-content-container {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.email-meta-info {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e6e9ed;
}

.email-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin-bottom: 12px;
  line-height: 1.4;
}

.email-meta {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.sender-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.detail-label {
  font-size: 13px;
  color: #666;
  margin-right: 8px;
  min-width: 60px;
}

.detail-value {
  font-size: 14px;
  color: #333;
}

.sender-name {
  font-weight: 500;
}

.sender-tag {
  color: #666;
  font-size: 13px;
}

.recipient {
  margin-right: 8px;
}

.email-date {
  font-size: 13px;
  color: #666;
  white-space: nowrap;
}

.email-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-top: 8px;
}

.email-tag {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
  font-weight: 500;
}

/* 附件样式 */
.attachments-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border: 1px solid #e6e9ed;
}

.attachments-header {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-weight: 500;
  color: #333;
}

.attachments-header .icon-small {
  width: 16px;
  height: 16px;
  margin-right: 8px;
  color: #666;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e6e9ed;
}

.attachment-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.file-icon {
  width: 20px;
  height: 20px;
  margin-right: 10px;
  color: #666;
}

.attachment-details {
  flex: 1;
}

.attachment-name {
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
}

.attachment-size {
  font-size: 12px;
  color: #666;
}

/* 邮件正文样式 */
.email-body-section {
  padding: 16px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e6e9ed;
}

.email-body {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  word-wrap: break-word;
  margin-bottom: 16px;
}

.email-body img {
  max-width: 100%;
  height: auto;
}

.email-signature {
  border-top: 1px solid #e6e9ed;
  padding-top: 16px;
  margin-top: 16px;
}

.signature-header {
  font-size: 13px;
  color: #666;
  margin-bottom: 8px;
  font-weight: 500;
}

.signature-content {
  font-size: 13px;
  color: #666;
  line-height: 1.5;
}
</style>
