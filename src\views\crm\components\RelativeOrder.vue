<template>
  <div
    v-loading="loading"
    v-empty="nopermission"
    class="rc-cont"
    xs-empty-icon="nopermission"
    xs-empty-text="暂无权限">
    <!-- <div
      v-if="!isSeas"
      class="rc-head">
      <el-button
        v-if="contractSave"
        icon="el-icon-plus"
        @click="createClick">新建订单</el-button>
    </div> -->
    <el-table
      :data="list"
      :height="tableHeight"
      :cell-class-name="cellClassName"
      stripe>
      <el-table-column
        v-for="(item, index) in fieldList"
        :key="index"
        :prop="item.prop"
        :label="item.label"
        show-overflow-tooltip>
        <template slot-scope="{ row, column, $index }">
          <template>
            {{ fieldFormatter(row, column) || '-'}}
          </template>
        </template>
      </el-table-column>
    </el-table>
    <div class="p-contianer">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="pageSizes"
          :page-size.sync="pageSize"
          :total="total"
          class="p-bar"
          background
          layout="prev, pager, next, sizes, total, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange" />
      </div>
  </div>
</template>

<script type="text/javascript">
import { queryErpOrderPageAPI } from '@/api/crm/customer'
import CheckStatusMixin from '@/mixins/CheckStatusMixin'

export default {
  name: 'RelativeOrder', // 相关联系人  可能再很多地方展示 放到客户管理目录下
  mixins: [CheckStatusMixin],
  props: {
    /** 模块ID */
    id: [String, Number],
    /** 没有值就是全部类型 有值就是当个类型 */
    crmType: {
      type: String,
      default: ''
    },
    /** 联系人人下 新建商机 需要联系人里的客户信息  合同下需要客户和商机信息 */
    detail: {
      type: Object,
      default: () => {
        return {}
      }
    }
  },
  data() {
    return {
      loading: false,
      nopermission: false,
      list: [],
      fieldList: [],
      tableHeight: '600px',
      contractId: '', // 查看全屏联系人详情的 ID
      // 创建的相关信息
      currentPage: 1,
      pageSize: 15,
      pageSizes: [15, 20, 30, 50],
      total: 0
    }
  },
  inject: ['rootTabs'],
  watch: {
    id(val) {
      this.list = []
      this.getDetail()
    },

    'rootTabs.currentName'(val) {
      if (val === 'RelativeOrder') {
        this.getDetail(false)
      }
    }
  },
  mounted() {
    this.getDetail()
  },
  methods: {
    getFieldList() {
      this.fieldList.push({ prop: 'billNo', width: '200', label: '订单编号' })
      this.fieldList.push({ prop: 'createDate', width: '200', label: '创建日期' })
      this.fieldList.push({
        prop: 'customerName',
        width: '250',
        label: '客户名称'
      })
      this.fieldList.push({
        prop: 'moldype',
        width: '200',
        label: '模具类型'
      })
      this.fieldList.push({
        prop: 'moldNo',
        width: '200',
        label: '模具编号'
      })
      this.fieldList.push({ prop: 'materialName', width: '200', label: '模具名称' })
      this.fieldList.push({ prop: 'materialModel', width: '200', label: '规格型号' })
      this.fieldList.push({ prop: 'qty', width: '100', label: '销售数量' })
      this.fieldList.push({ prop: 'unitName', width: '100', label: '销售单位' })
      this.fieldList.push({ prop: 'taxPrice', width: '200', label: '含税单价' })
      this.fieldList.push({ prop: 'taxRate', width: '200', label: '税率%' })
      this.fieldList.push({ prop: 'allAmount', width: '200', label: '价税合计' })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getDetail()
    },

    /**
     * 更改当前页数
     */
    handleCurrentChange(val) {
      this.currentPage = val
      this.getDetail()
    },

    getDetail(loading = true) {
      this.loading = loading
      const params = 
        { 
          customerId: this.id,
          limit: this.pageSize,
          page: this.currentPage
        }
      queryErpOrderPageAPI(params)
        .then(res => {
          if (this.fieldList.length == 0) {
            this.getFieldList()
          }
          this.nopermission = false
          this.loading = false
          this.list = res.data.records
          this.total = parseInt(res.data.total);
        })
        .catch(data => {
          if (data.code == 102) {
            this.nopermission = true
          }
          this.loading = false
        })
    },

    /**
     * 格式化字段
     */
    fieldFormatter(row, column) {
      // 如果需要格式化
      if(column.property == 'qty'){
         return parseInt(row[column.property] || 0)
      }
      if (column.property == 'taxPrice' || column.property == 'allAmount') {
        return Number(row[column.property]).toFixed(2)
      }
      if (column.property === 'createDate') {
        return (row[column.property]).split(' ')[0]
      }
      if (column.property === 'taxRate') {
        return Number(row[column.property])
      }
      return row[column.property] == ' ' || row[column.property] == null ? '--' : row[column.property]
    },

    /**
     * 当某一行被点击时会触发该事件
     */

    /**
     * @description: 状态颜色
     * @param {*} status
     * @return {*}
     */
    getStatusStyle(status) {
      return {
        backgroundColor: this.getStatusColor(status)
      }
    },

    /**
     * 通过回调控制class
     */
    cellClassName({ row, column, rowIndex, columnIndex }) {
      if (column.property === 'contractName') {
        return 'can-visit--underline'
      } else if (column.property === 'money') {
        return 'is-floatnumber-column'
      } else {
        return ''
      }
    }
  }
}
</script>
<style lang="scss" scoped>
@import "../styles/relativecrm.scss";
@import "../styles/table.scss";
</style>
