<template>
  <div>
    <div v-if="!isCurrentUser && isEmpty(customerInfo)" class="stranger-customer-card">
      <div class="person-info-header">
        <div class="person-name" @click="copyEmail(selectedEmail)">
          {{ selectedEmail }}
        </div>
        <div class="stranger-tag">陌生</div>
      </div>
      <div class="action-buttons">
        <button class="action-btn add-customer-btn" @click="addNewCustomer">
          新增客户
        </button>
        <button class="action-btn add-contact-btn" @click="addNewContact">
          新增联系人
        </button>
      </div>
    </div>
    <div v-if="isCurrentUser" class="existing-customer-card">
      <div class="person-info-header">
        <div class="person-name">
          {{ composeData && composeData.userName }}
        </div>
        <div class="stranger-tag">本人</div>
      </div>
      <div class="person-details">
        <div class="detail-row">
          <span class="label">邮箱：</span>
          <span class="value">{{ selectedEmail }}</span>
        </div>
      </div>
    </div>
    <div v-if="!isCurrentUser && !isEmpty(customerInfo)" class="existing-customer-card">
      <div class="person-info-header">
        <div class="person-name">{{ customerInfo.customerName ||
          extractUserName(selectedEmail) }}</div>
        <div class="stranger-tag">客户</div>
      </div>
      <div class="person-details">
        <div class="detail-row" v-if="customerInfo.customerId">
          <span class="label">客户编号：</span>
          <span class="value">{{ customerInfo.customerId }}</span>
        </div>
        <div class="detail-row" v-if="customerInfo.ownerUserName">
          <span class="label">所属人：</span>
          <span class="value">{{ customerInfo.ownerUserName }}</span>
        </div>
        <div class="detail-row" v-if="customerInfo.customerStatus">
          <span class="label">客户状态：</span>
          <span class="value">{{ customerInfo.customerStatus }}</span>
        </div>
        <div class="detail-row" v-if="customerInfo.country">
          <span class="label">国家地区：</span>
          <span class="value">{{ customerInfo.country }}</span>
        </div>
        <div class="detail-row" v-if="customerInfo.crmContactList">
          <span class="label">联系人：</span>
          <span class="value">{{extractUserName(selectedEmail) }}</span>
        </div>
        <div class="detail-row" v-if="customerInfo.crmContactList">
          <span class="label">联系人邮箱：</span>
          <span class="value">{{ selectedEmail }}</span>
        </div>
         <div class="location-info" v-if="customerInfo.locationInfo">
          📍 {{ customerInfo.locationInfo }}
        </div>
        <div class="action-buttons">
          <button class="action-btn add-customer-btn" @click="writeEmail(selectedEmail)">
            写邮件
          </button>
          <button class="action-btn add-contact-btn" @click="receivableEmail(selectedEmail)">
            往来邮件
          </button>
        </div>
      </div>
    </div>

    <!-- 新增客户弹窗 -->
    <customer-create append-to-body v-if="showCreateCustomer" crm-type="customer" :action="createAction"
      @close="closeCreateCustomer" @save-success="handleCreateSuccess" />

    <!-- 新增联系人弹窗 -->
    <contacts-create append-to-body v-if="showCreateContact" :action="createAction" @close="closeCreateContact"
      @save-success="handleContactCreateSuccess" />
  </div>
</template>
<script>
import { isEmpty } from '@/utils/types'
import CustomerCreate from '@/views/crm/customer/Create'
import ContactsCreate from '@/views/crm/contacts/Create'

export default {
  name: 'customerCard',
  components: {
    CustomerCreate,
    ContactsCreate
  },
  props: {
    customerInfo: {
      type: Object,
      default: () => {
        return {}
      }
    },
    composeData: {
      type: Object,
      default: () => {
        return {}
      }
    },
    selectedEmail: {
      type: String,
      default: ''
    },
  },
  data() {
    return {
      showCreateCustomer: false,
      createAction: {
        type: 'save',
        id: '',
        data: {}
      },
      showCreateContact: false
    }
  },
  computed:{
    isCurrentUser(){
      return this.selectedEmail == this.composeData.from
    }
  },
  methods: {
    isEmpty,
    copyEmail(email) {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(email).then(() => {
          this.$message.success('邮箱地址已复制到剪贴板');
        }).catch(() => {
          this.$message.error('复制失败');
        });
      }
    },
    writeEmail(email){
      const timestamp = Date.now();
      this.$router.push({
        path: `/crm/email/subs/compose/new/${timestamp}`,
        query: {
          from: this.composeData.from,
          userId: this.composeData.userId,
          to: email,
        }
      });
    },
    receivableEmail(email){
      // 触发父组件的往来邮件事件
      this.$emit('open-email-exchange', email);
    },
    extractUserName(email) {
      if (!email) return '';
      return email.split('@')[0];
    },
    addNewCustomer() {
      this.createAction = {
        type: 'save',
        id: '',
        data: {
          email: this.selectedEmail,
        }
      }
      this.showCreateCustomer = true
      // 通知父组件隐藏tooltip
      this.$emit('hide-tooltip')
    },
    addNewContact() {
      // 预填充邮箱信息到联系人创建表单
      this.createAction = {
        type: 'save',
        id: '',
        data: {
          // 预填充邮箱字段
          email: this.selectedEmail,
          // 预填充联系人姓名字段（使用邮箱@前缀作为姓名）
          name: this.extractUserName(this.selectedEmail)
        }
      }
      this.showCreateContact = true
      // 通知父组件隐藏tooltip
      this.$emit('hide-tooltip')
    },
    closeCreateCustomer() {
      this.showCreateCustomer = false
    },
    closeCreateContact() {
      this.showCreateContact = false
    },
    handleCreateSuccess(data) {
      console.log("有调用这里吗", data);
      this.showCreateCustomer = false;
    },
    handleContactCreateSuccess(data) {
      console.log("联系人创建成功", data);
      this.showCreateContact = false;
    }
  }
}
</script>
<style lang="scss" scoped>
/* 陌生客户卡片样式 - 与已建档客户保持一致 */
.stranger-customer-card {
  background: white;
  overflow: hidden;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 380px;
}

.existing-customer-card {
  background: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  min-width: 320px;
}

.stranger-customer-card .person-info-header,
.existing-customer-card .person-info-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  gap: 12px;
}

.stranger-customer-card .person-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.stranger-customer-card .person-email {
  font-size: 14px;
  opacity: 0.9;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* 陌生客户详情区域 - 模拟已建档客户的结构 */
.stranger-customer-card .person-details {
  padding: 16px;
}

.stranger-customer-card .detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.stranger-customer-card .detail-row .label {
  color: #666;
  font-weight: 500;
  min-width: 80px;
}

.stranger-customer-card .detail-row .value {
  color: #333;
  text-align: right;
  flex: 1;
}

.stranger-customer-card .stranger-status {
  margin: 12px 0;
  padding: 8px;
  background: #f0f9ff;
  border-radius: 4px;
  font-size: 13px;
  color: #1890ff;
  text-align: center;
  border: 1px solid #91d5ff;
}

.action-buttons {
  padding: 16px;
  display: flex;
  gap: 12px;
  background: #fafbfc;
  border-top: 1px solid #f0f0f0;
}

.action-btn {
  flex: 1;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  background: white;
  color: #333;
}


.add-customer-btn {
  color: #1890ff;
  border-color: #1890ff;
}

.add-contact-btn {
  color: #1890ff;
  border-color: #1890ff;
}

.person-details {
  padding: 16px;
}

.person-info-tooltip {
  max-width: 350px;
  padding: 0;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  overflow: hidden;
}

.person-name {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 4px;
}

.person-email {
  font-size: 14px;
  opacity: 0.9;
}

.copy-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 4px 6px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 20px;
  margin-left: 4px;
}

.copy-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: scale(1.05);
}

.stranger-tag {
  top: 12px;
  right: 12px;
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.detail-row .label {
  color: #666;
  font-weight: 500;
  min-width: 80px;
}

.detail-row .value {
  color: #333;
  text-align: right;
  flex: 1;
}

.email-stats {
  margin: 12px 0;
  padding: 8px;
  background: #f8f9fa;
  border-radius: 4px;
  font-size: 13px;
}

.stat-item {
  display: inline-block;
  margin-right: 16px;
  color: #666;
}

.location-info {
  margin-top: 8px;
  padding: 8px;
  background: #e8f4fd;
  border-radius: 4px;
  font-size: 13px;
  color: #1890ff;
}
</style>