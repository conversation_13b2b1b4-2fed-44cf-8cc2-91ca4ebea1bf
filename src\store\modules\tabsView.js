import { queryEmailDetailsAPI } from '@/api/crm/email'

const state = {
  visitedViews: [],
  cachedViews: []
}

const mutations = {
  ADD_VISITED_VIEW(state, view) {
    if (state.visitedViews.some(v => v.path === view.path)) return

    let title = view.meta.title || '未命名页面'

    if (view.name === 'EmailComposePage') {
      let mode = view.params?.mode
      if (!mode) {
        const segments = view.path.split('/')
        mode = segments[segments.length - 2] // 倒数第二段是 mode
      }

      const titleMap = {
        compose: '新写邮件',
        replynormal: '回复邮件',
        replywithattachment:'回复带附件',
        replywithoutoriginal:'回复不带原文',
        replyAllnormal:'回复全部',
        replyAllwithattachment:'回复全部带附件',
        replyAllwithoutoriginal:'回复全部不带原文',
        forwardnormal: '转发邮件',
        forwardwithoutoriginal:'转发不带原文'
      }
      title = titleMap[mode] || '写邮件'
    }

    // 先添加基础的 visited view
    const visitedView = {
      title,
      path: view.path,
      name: view.name
    }
    state.visitedViews.push(visitedView)

    if (view.name && !state.cachedViews.includes(view.name)) {
      state.cachedViews.push(view.name)
    }
  },

  // 新增：更新已存在的 visited view 的标题
  UPDATE_VISITED_VIEW_TITLE(state, { path, title }) {
    const view = state.visitedViews.find(v => v.path === path)
    if (view) {
      view.title = title
    }
  },
  REMOVE_VISITED_VIEW(state, path) {
    const view = state.visitedViews.find(v => v.path === path)
    if (view) {
      state.cachedViews = state.cachedViews.filter(name => name !== view.name)
    }
    state.visitedViews = state.visitedViews.filter(v => v.path !== path)
  }
}

const actions = {
  async addView({ commit }, view) {
    // 先添加基础的 visited view
    commit('ADD_VISITED_VIEW', view)

    // 如果是邮件编写页面，尝试获取原邮件主题来更新标题
    if (view.name === 'EmailComposePage') {
      const mode = view.params?.mode
      const originalEmailId = view.query?.originalEmailId

      // 只有在回复、回复全部、转发模式下且有原邮件ID时才获取详情
      if (['replynormal', 'replywithattachment','replywithoutoriginal', 'replyAllnormal','replyAllwithattachment','replyAllwithoutoriginal','forwardnormal','forwardwithoutoriginal'].includes(mode) && originalEmailId) {
        try {
          const response = await queryEmailDetailsAPI({ id: originalEmailId })
          const emailDetail = response.data

          if (emailDetail && emailDetail.subject) {
            // 根据模式和原邮件主题生成新标题
            let newTitle = ''
            const originalSubject = emailDetail.subject

            switch (mode) {
              case 'replynormal':
                newTitle = `回复: ${originalSubject}`
                break
              case 'replywithattachment':
                newTitle = `回复: ${originalSubject}`
                break
              case 'replywithoutoriginal':
                newTitle = `回复: ${originalSubject}`
                break
              case 'replyAllnormal':
                newTitle = `回复全部: ${originalSubject}`
                break
              case 'replyAllwithattachment':
                newTitle = `回复全部: ${originalSubject}`
                break
              case 'replyAllwithoutoriginal':
                newTitle = `回复全部: ${originalSubject}`
                break
              case 'forwardnormal':
                newTitle = `转发: ${originalSubject}`
                break
              case 'forwardwithoutoriginal':
                newTitle = `转发: ${originalSubject}`
                break
              default:
                newTitle = `写邮件: ${originalSubject}`
            }

            // 更新标题
            commit('UPDATE_VISITED_VIEW_TITLE', {
              path: view.path,
              title: newTitle
            })
          }
        } catch (error) {
          console.warn('获取原邮件详情失败，使用默认标题:', error)
          // 获取失败时保持原有的默认标题，不做额外处理
        }
      }
    }
  },
  removeView({ commit }, path) {
    commit('REMOVE_VISITED_VIEW', path)
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}