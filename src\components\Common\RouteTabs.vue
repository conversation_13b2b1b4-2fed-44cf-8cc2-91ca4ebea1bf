<template>
  <div class="route-tabs">
    <el-tabs v-model="activePath" type="card" closable @tab-click="handleClick" @tab-remove="handleClose">
      <el-tab-pane v-for="tab in visitedViews" :key="tab.path" :name="tab.path">
        <template slot="label">
          <el-tooltip :content="tab.title" :disabled="!shouldShowTooltip(tab.title)" placement="bottom" effect="dark">
            <span class="tab-title">{{ tab.title }}</span>
          </el-tooltip>
        </template>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  computed: {
    visitedViews() {
      return this.$store.state.tabsView.visitedViews
    },
    activePath: {
      get() {
        return this.$route.path
      },
      set(val) {
        this.$router.push(val)
      }
    }
  },
  methods: {
    handleClick(tab) {
      this.$router.push(tab.name)
    },
    handleClose(path) {
      this.$store.dispatch('tabsView/removeView', path)
      if (this.$route.path === path) {
        const last = this.visitedViews[this.visitedViews.length - 1]
        this.$router.push(last?.path || '/')
      }
    },
    // 判断是否需要显示 Tooltip
    shouldShowTooltip(title) {
      // 当标题长度超过一定字符数时显示 Tooltip
      // 中文字符按2个字符计算，英文字符按1个字符计算
      const maxLength = 6 // 最大显示字符数
      let length = 0
      for (let i = 0; i < title.length; i++) {
        // 判断是否为中文字符
        if (title.charCodeAt(i) > 127) {
          length += 2
        } else {
          length += 1
        }
      }
      return length > maxLength
    }
  }
}
</script>

<style scoped>
.route-tabs {
  background: #fff;
  padding: 0 16px;
}

/* Tab 标题样式 */
.tab-title {
  display: inline-block;
  min-width: 20px;
  /* 确保至少显示两个中文字符的宽度 */
  max-width: 120px;
  /* 调整最大宽度，为关闭按钮预留空间 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  vertical-align: middle;
  line-height: 16px;
  /* 与关闭按钮高度保持一致 */
}
</style>